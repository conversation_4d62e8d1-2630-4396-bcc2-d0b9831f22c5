import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Upload, 
  Search, 
  Filter, 
  RefreshCw, 
  Download, 
  Trash2, 
  Eye, 
  ToggleLeft, 
  ToggleRight,
  AlertCircle,
  CheckCircle,
  HardDrive,
  Wifi,
  Bluetooth
} from 'lucide-react';
import { Button } from '../ui/button';
import { 
  Software, 
  SoftwareFilters, 
  getSoftwareList, 
  updateSoftwareStatus, 
  downloadSoftware, 
  deleteSoftware,
  formatFileSize,
  formatDate
} from '../../utils/api/softwareApi';
import { SoftwareUploadModal } from './SoftwareUploadModal';
import { SoftwareDetailModal } from './SoftwareDetailModal';

export function SoftwareManagementTab() {
  const { t } = useTranslation();
  const [software, setSoftware] = useState<Software[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<SoftwareFilters>({
    page: 1,
    limit: 20,
    sortBy: 'uploadDate',
    sortOrder: 'desc'
  });
  const [totalPages, setTotalPages] = useState(0);
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [selectedSoftware, setSelectedSoftware] = useState<Software | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  // 載入軟體列表
  const loadSoftware = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await getSoftwareList(filters);
      setSoftware(result.software);
      setTotalPages(result.totalPages);
    } catch (err) {
      setError(err instanceof Error ? err.message : '載入軟體列表失敗');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSoftware();
  }, [filters]);

  // 處理搜尋
  const handleSearch = (searchTerm: string) => {
    setFilters(prev => ({ ...prev, search: searchTerm, page: 1 }));
  };

  // 處理過濾
  const handleFilter = (key: keyof SoftwareFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value === 'all' ? undefined : value, page: 1 }));
  };

  // 處理狀態切換
  const handleToggleStatus = async (id: string, isEnabled: boolean) => {
    try {
      await updateSoftwareStatus(id, { isEnabled: !isEnabled });
      await loadSoftware();
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新狀態失敗');
    }
  };

  // 處理下載
  const handleDownload = async (id: string, type: 'original' | 'pure' = 'original') => {
    try {
      await downloadSoftware(id, type);
    } catch (err) {
      setError(err instanceof Error ? err.message : '下載失敗');
    }
  };

  // 處理刪除
  const handleDelete = async (id: string) => {
    if (!confirm('確定要刪除這個軟體嗎？此操作無法復原。')) {
      return;
    }

    try {
      await deleteSoftware(id);
      await loadSoftware();
    } catch (err) {
      setError(err instanceof Error ? err.message : '刪除失敗');
    }
  };

  // 處理查看詳細資訊
  const handleViewDetail = (software: Software) => {
    setSelectedSoftware(software);
    setIsDetailModalOpen(true);
  };

  // 處理軟體更新
  const handleSoftwareUpdate = (updatedSoftware?: Software) => {
    if (updatedSoftware) {
      // 如果提供了更新後的軟體物件，更新選中的軟體和列表中的對應項目
      setSelectedSoftware(updatedSoftware);
      setSoftware(prevSoftware =>
        prevSoftware.map(item =>
          item._id === updatedSoftware._id ? updatedSoftware : item
        )
      );
    } else {
      // 如果沒有提供更新後的軟體物件，重新載入整個列表
      loadSoftware();
    }
  };

  // 獲取設備類型圖標
  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'gateway':
        return <HardDrive className="w-4 h-4" />;
      case 'epd':
        return <HardDrive className="w-4 h-4" />;
      default:
        return <HardDrive className="w-4 h-4" />;
    }
  };

  // 獲取功能類型圖標
  const getFunctionIcon = (functionType: string) => {
    switch (functionType) {
      case 'wifi':
        return <Wifi className="w-4 h-4" />;
      case 'ble':
        return <Bluetooth className="w-4 h-4" />;
      default:
        return <HardDrive className="w-4 h-4" />;
    }
  };

  // 獲取狀態顏色
  const getStatusColor = (software: Software) => {
    if (!software.isEnabled) return 'text-gray-500';
    if (software.status === 'active') return 'text-green-600';
    if (software.status === 'deprecated') return 'text-yellow-600';
    return 'text-gray-500';
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">軟體管理</h2>
        <p className="text-gray-600">管理系統軟體，包括Gateway和EPD設備的韌體</p>
      </div>

      {/* 錯誤提示 */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
          <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
          <span className="text-red-700">{error}</span>
          <button 
            onClick={() => setError(null)}
            className="ml-auto text-red-500 hover:text-red-700"
          >
            ×
          </button>
        </div>
      )}

      {/* 工具列 */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="flex gap-2">
          <Button
            onClick={() => setIsUploadModalOpen(true)}
            className="bg-orange-500 hover:bg-orange-600 text-white"
          >
            <Upload className="w-4 h-4 mr-2" />
            上傳軟體
          </Button>
          <Button
            onClick={loadSoftware}
            variant="outline"
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            重新整理
          </Button>
        </div>

        <div className="flex gap-2 flex-1">
          {/* 搜尋框 */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="搜尋軟體名稱、版本..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              value={filters.search || ''}
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>

          {/* 過濾器 */}
          <select
            value={filters.deviceType || 'all'}
            onChange={(e) => handleFilter('deviceType', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            <option value="all">所有設備</option>
            <option value="gateway">Gateway</option>
            <option value="epd">EPD</option>
          </select>

          <select
            value={filters.functionType || 'all'}
            onChange={(e) => handleFilter('functionType', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            <option value="all">所有功能</option>
            <option value="wifi">WiFi</option>
            <option value="ble">BLE</option>
          </select>

          <select
            value={filters.status || 'all'}
            onChange={(e) => handleFilter('status', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            <option value="all">所有狀態</option>
            <option value="active">啟用</option>
            <option value="disabled">禁用</option>
            <option value="deprecated">已棄用</option>
          </select>
        </div>
      </div>

      {/* 軟體列表 */}
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <RefreshCw className="w-8 h-8 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-600">載入中...</span>
        </div>
      ) : software.length === 0 ? (
        <div className="text-center py-12">
          <HardDrive className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">沒有找到軟體</h3>
          <p className="text-gray-500 mb-4">
            {filters.search || filters.deviceType || filters.functionType || filters.status
              ? '請調整搜尋條件或過濾器'
              : '開始上傳第一個軟體'}
          </p>
          <Button
            onClick={() => setIsUploadModalOpen(true)}
            className="bg-orange-500 hover:bg-orange-600 text-white"
          >
            <Upload className="w-4 h-4 mr-2" />
            上傳軟體
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {software.map((item) => (
            <div key={item._id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
              {/* 軟體標題 */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1 min-w-0 pr-3">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1 truncate" title={item.name}>
                    {item.name}
                  </h3>
                  <div className="flex items-center gap-2 text-sm text-gray-600 mb-1 flex-wrap">
                    <span className="bg-gray-100 px-2 py-1 rounded">v{item.version}</span>
                    <div className="flex items-center gap-1">
                      {getDeviceIcon(item.deviceType)}
                      <span className="capitalize">{item.deviceType}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      {getFunctionIcon(item.functionType)}
                      <span className="uppercase">{item.functionType}</span>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500">
                    <span>型號: {item.deviceModelName || `型號 ${item.deviceModel}`}</span>
                    {item.minHwVersion && item.maxHwVersion && (
                      <span className="ml-2">
                        硬體版本: {item.minHwVersion} - {item.maxHwVersion}
                      </span>
                    )}
                  </div>
                </div>
                <div className="flex-shrink-0">
                  <button
                    onClick={() => handleToggleStatus(item._id, item.isEnabled)}
                    className={`p-1 rounded ${item.isEnabled ? 'text-green-600' : 'text-gray-400'}`}
                    title={item.isEnabled ? '點擊禁用' : '點擊啟用'}
                  >
                    {item.isEnabled ? <ToggleRight className="w-6 h-6" /> : <ToggleLeft className="w-6 h-6" />}
                  </button>
                </div>
              </div>

              {/* 軟體資訊 */}
              <div className="space-y-2 mb-4 text-sm text-gray-600">
                <div className="flex justify-between">
                  <span>韌體大小:</span>
                  <span>{formatFileSize(item.binSize || item.fileSize)}</span>
                </div>
                <div className="flex justify-between">
                  <span>上傳日期:</span>
                  <span>{formatDate(item.uploadDate)}</span>
                </div>
                <div className="flex justify-between">
                  <span>CRC校驗:</span>
                  <span className="font-mono text-xs">{item.checksum}</span>
                </div>
                <div className="flex justify-between">
                  <span>狀態:</span>
                  <span className={getStatusColor(item)}>
                    {item.isEnabled ? '啟用' : '禁用'}
                  </span>
                </div>
              </div>

              {/* 操作按鈕 */}
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleViewDetail(item)}
                  className="flex-1"
                >
                  <Eye className="w-4 h-4 mr-1" />
                  詳細
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleDownload(item._id, 'original')}
                  className="flex-1"
                >
                  <Download className="w-4 h-4 mr-1" />
                  下載
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleDelete(item._id)}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 分頁 */}
      {totalPages > 1 && (
        <div className="mt-6 flex justify-center">
          <div className="flex gap-2">
            <Button
              variant="outline"
              disabled={filters.page === 1}
              onClick={() => setFilters(prev => ({ ...prev, page: (prev.page || 1) - 1 }))}
            >
              上一頁
            </Button>
            <span className="px-4 py-2 text-sm text-gray-600">
              第 {filters.page} 頁，共 {totalPages} 頁
            </span>
            <Button
              variant="outline"
              disabled={filters.page === totalPages}
              onClick={() => setFilters(prev => ({ ...prev, page: (prev.page || 1) + 1 }))}
            >
              下一頁
            </Button>
          </div>
        </div>
      )}

      {/* 上傳模態窗口 */}
      <SoftwareUploadModal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        onSuccess={() => {
          setIsUploadModalOpen(false);
          loadSoftware();
        }}
      />

      {/* 詳細資訊模態窗口 */}
      {selectedSoftware && (
        <SoftwareDetailModal
          isOpen={isDetailModalOpen}
          onClose={() => {
            setIsDetailModalOpen(false);
            setSelectedSoftware(null);
          }}
          software={selectedSoftware}
          onUpdate={handleSoftwareUpdate}
        />
      )}
    </div>
  );
}
